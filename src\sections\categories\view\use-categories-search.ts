import { useState, useEffect, useCallback } from 'react';
import { useCategoriesApi, CategoriesQueryParams } from 'src/services/api/use-categories-api';

// Custom hook for categories search with debounce
export const useCategoriesSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const { useGetCategories } = useCategoriesApi();

  // Debounce search query
  useEffect(() => {
    // If search query is empty, immediately clear the debounced query
    if (searchQuery.trim() === '') {
      setDebouncedQuery('');
      setIsSearching(false);
      return () => {}; // Return empty cleanup function for consistency
    }

    // Set searching state when query changes
    setIsSearching(true);

    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
      setIsSearching(false);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchQuery]); // Remove debouncedQuery from dependencies to prevent loops

  // Prepare query parameters
  const queryParams: CategoriesQueryParams = {
    take: 15,
    skip: 0,
    ...(debouncedQuery && { name: debouncedQuery }),
  };

  // Get categories with search parameters
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategories(queryParams);

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {

    setSearchQuery(event.target.value);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  return {
    searchQuery,
    debouncedQuery,
    isSearching,
    categoriesResponse,
    isLoading: isLoading || isSearching,
    isError,
    handleSearchChange,
    clearSearch,
    refetch,
  };
};

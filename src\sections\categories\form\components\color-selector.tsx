import {
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormHelperText,
  Stack,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  IconButton,
  Popover,
} from '@mui/material';
import { useState } from 'react';
import { Iconify } from 'src/components/iconify';
import { Controller, Control, useWatch } from 'react-hook-form';
import { CategoryFormValues } from '../category-schema';

interface ColorOption {
  value: string;
  label: string;
  color: string;
  icon?: string;
}

interface ColorSelectorProps {
  control: Control<CategoryFormValues>;
  error?: boolean;
  helperText?: string;
}

export default function ColorSelector({ control, error, helperText }: ColorSelectorProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  // Watch both colorType and customColor to synchronize them
  const colorType = useWatch({ control, name: 'colorType' });
  const customColor = useWatch({ control, name: 'customColor' });

  const colorOptions: ColorOption[] = [
    { value: 'primary', label: 'Primary', color: theme.palette.primary.main },
    { value: 'secondary', label: 'Secondary', color: theme.palette.secondary.main },
    { value: 'success', label: 'Success', color: theme.palette.success.main },
    { value: 'warning', label: 'Warning', color: theme.palette.warning.main },
    { value: 'info', label: 'Info', color: theme.palette.info.main },
    { value: 'error', label: 'Error', color: theme.palette.error.main },
  ];

  // Helper function to validate hex color
  const isValidHexColor = (color: string): boolean => {
    const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexPattern.test(color);
  };

  // Helper function to get current display color
  const getCurrentColor = (): string => {
    if (colorType === 'custom' && customColor && isValidHexColor(customColor)) {
      return customColor;
    }
    const predefinedOption = colorOptions.find(option => option.value === colorType);
    return predefinedOption?.color || theme.palette.primary.main;
  };

  // Helper function to get current display label
  const getCurrentLabel = (): string => {
    if (colorType === 'custom') {
      return customColor || 'Custom';
    }
    const predefinedOption = colorOptions.find(option => option.value === colorType);
    return predefinedOption?.label || 'Primary';
  };

  const handleOpenPopover = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Controller
        name="colorType"
        control={control}
        render={({ field }) => {
          return (
            <Stack spacing={1}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Choose Color
              </Typography>
              <Box
                component="button"
                type="button"
                onClick={handleOpenPopover}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  whiteSpace: 'nowrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  p: 2,
                  border: (theme) => `1px solid ${theme.palette.divider}`,
                  borderRadius: 2,
                  bgcolor: 'background.paper',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                  '&:focus': {
                    outline: 'none',
                    borderColor: 'primary.main',
                  },
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      bgcolor: getCurrentColor(),
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: 80,
                    }}
                  >
                    {getCurrentLabel()}
                  </Typography>
                </Stack>
                <Iconify icon="eva:chevron-down-fill" width={16} height={16} />
              </Box>

              <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleClosePopover}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                slotProps={{
                  paper: {
                    sx: {
                      p: 2,
                      width: 500,
                      maxHeight: 300,
                    },
                  },
                }}
              >
                <Stack spacing={2}>
                  <Typography variant="subtitle1">Select Color</Typography>

                  <FormControl component="fieldset" error={error}>
                    {/* Enhanced Custom Color Input */}
                    <Controller
                      name="customColor"
                      control={control}
                      render={({
                        field: customColorField,
                        fieldState: { error: customColorError },
                      }) => (
                        <Box sx={{ mx: '-10px', mb: 1 }}>
                          <TextField
                            {...customColorField}
                            label="Custom Color (Hex)"
                            placeholder="#FF5733"
                            error={!!customColorError}
                            helperText={customColorError?.message}
                            onChange={(e) => {
                              const newValue = e.target.value;
                              customColorField.onChange(newValue);

                              // If it's a valid hex color, automatically set colorType to custom
                              if (isValidHexColor(newValue)) {
                                field.onChange('custom');
                              }
                            }}
                            onKeyDown={(e) => {
                              // Prevent form submission on Enter key
                              if (e.key === 'Enter') {
                                e.preventDefault();
                              }
                            }}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Box
                                    sx={{
                                      width: 20,
                                      height: 20,
                                      borderRadius: '50%',
                                      bgcolor: isValidHexColor(customColorField.value || '')
                                        ? customColorField.value
                                        : '#FF5733',
                                      mr: 1,
                                      border: '1px solid',
                                      borderColor: 'divider',
                                    }}
                                  />
                                </InputAdornment>
                              ),
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    type="button"
                                    sx={{ p: 0.5 }}
                                    edge="end"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const input = document.createElement('input');
                                      input.type = 'color';
                                      input.value = isValidHexColor(customColorField.value || '')
                                        ? (customColorField.value || '#FF5733')
                                        : '#FF5733';
                                      input.onchange = (event) => {
                                        const target = event.target as HTMLInputElement;
                                        const newColor = target.value;
                                        customColorField.onChange(newColor);
                                        field.onChange('custom');
                                      };
                                      input.click();
                                    }}
                                  >
                                    <Box
                                      component="input"
                                      type="color"
                                      value={isValidHexColor(customColorField.value || '')
                                        ? customColorField.value
                                        : '#FF5733'}
                                      onChange={(e) => {
                                        const newColor = e.target.value;
                                        customColorField.onChange(newColor);
                                        field.onChange('custom');
                                      }}
                                      sx={{
                                        width: '100%',
                                        height: '100%',
                                        border: 'none',
                                        padding: 0,
                                        cursor: 'pointer',
                                        opacity: 0,
                                        position: 'absolute',
                                      }}
                                    />
                                    <Iconify icon="eva:color-palette-fill" width={24} height={24} />
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>
                      )}
                    />

                    {/* Predefined Color Swatches */}
                    <RadioGroup
                      {...field}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        // Clear custom color when selecting predefined color
                        if (e.target.value !== 'custom') {
                          // We need to access the customColor field to clear it
                          // This will be handled by the form validation
                        }
                        handleClosePopover();
                      }}
                    >
                      <Box
                        sx={{
                          display: 'grid',
                          gridTemplateColumns: 'repeat(6, 1fr)',
                          gap: 0,
                        }}
                      >
                        {colorOptions.map((option) => (
                          <FormControlLabel
                            key={option.value}
                            value={option.value}
                            control={
                              <Radio
                                sx={{
                                  '&.Mui-checked': {
                                    color: option.color,
                                  },
                                }}
                                icon={
                                  <Box
                                    sx={{
                                      width: 20,
                                      height: 20,
                                      borderRadius: '50%',
                                      bgcolor: option.color,
                                      opacity: 0.6,
                                    }}
                                  />
                                }
                                checkedIcon={
                                  <Box
                                    sx={{
                                      width: 20,
                                      height: 20,
                                      borderRadius: '50%',
                                      bgcolor: option.color,
                                      boxShadow: `0 0 0 2px ${theme.palette.background.paper}, 0 0 0 4px ${option.color}`,
                                    }}
                                  />
                                }
                              />
                            }
                            label=""
                          />
                        ))}
                      </Box>
                    </RadioGroup>

                    {helperText && <FormHelperText>{helperText}</FormHelperText>}
                  </FormControl>
                </Stack>
              </Popover>
            </Stack>
          );
        }}
      />
    </>
  );
}
